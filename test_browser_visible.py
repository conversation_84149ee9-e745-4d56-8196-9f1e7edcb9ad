#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試瀏覽器是否真的啟動並可見
"""

import os
import subprocess
import time

def create_test_page():
    """創建一個明顯的測試頁面"""
    html_content = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>🎉 瀏覽器啟動成功測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-align: center;
            padding: 50px;
            margin: 0;
        }
        .container {
            background: rgba(0,0,0,0.7);
            padding: 40px;
            border-radius: 20px;
            display: inline-block;
        }
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .success {
            font-size: 1.5em;
            margin: 20px 0;
        }
        .time {
            font-size: 1.2em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 瀏覽器啟動成功！</h1>
        <div class="success">✅ 如果您看到這個頁面，說明瀏覽器確實啟動了！</div>
        <div class="time">測試時間: <span id="time"></span></div>
        <div style="margin-top: 30px;">
            <p>這證明瀏覽器啟動策略是有效的。</p>
            <p>現在可以嘗試連接到 MCP Feedback Enhanced Web UI。</p>
        </div>
    </div>
    
    <script>
        document.getElementById('time').textContent = new Date().toLocaleString();
        
        // 每秒更新時間，證明頁面是活的
        setInterval(function() {
            document.getElementById('time').textContent = new Date().toLocaleString();
        }, 1000);
        
        // 在控制台輸出成功信息
        console.log('🎉 瀏覽器啟動成功測試頁面已載入！');
        console.log('時間:', new Date().toLocaleString());
    </script>
</body>
</html>"""
    
    test_file = os.path.expanduser("~/browser_test_success.html")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return test_file

def test_browser_launch():
    """測試瀏覽器啟動"""
    print("=== 測試瀏覽器是否真的啟動 ===")
    
    # 創建測試頁面
    test_file = create_test_page()
    print(f"✅ 創建測試頁面: {test_file}")
    
    # 嘗試啟動瀏覽器
    commands = [
        ['xdg-open', test_file],
        ['google-chrome', test_file],
        ['firefox', test_file],
    ]
    
    success_count = 0
    
    for cmd in commands:
        try:
            print(f"\n嘗試命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, timeout=5)
            
            if result.returncode == 0:
                print(f"✅ 命令執行成功: {cmd[0]}")
                success_count += 1
                
                # 給瀏覽器時間啟動
                print("等待 3 秒讓瀏覽器啟動...")
                time.sleep(3)
                
                # 如果成功，就不嘗試其他命令了
                break
            else:
                print(f"❌ 命令失敗: {cmd[0]}, 返回碼: {result.returncode}")
                if result.stderr:
                    stderr = result.stderr.decode()[:200]
                    print(f"錯誤: {stderr}")
                    
        except FileNotFoundError:
            print(f"❌ 命令不存在: {cmd[0]}")
        except subprocess.TimeoutExpired:
            print(f"⏰ 命令超時: {cmd[0]} (可能成功啟動)")
            success_count += 1
            break
        except Exception as e:
            print(f"❌ 命令異常: {cmd[0]}, 錯誤: {e}")
    
    print(f"\n=== 測試結果 ===")
    if success_count > 0:
        print("✅ 至少有一個命令執行成功")
        print("🔍 請檢查是否有瀏覽器窗口開啟並顯示彩色測試頁面")
        print("📄 如果看到測試頁面，說明瀏覽器確實啟動了")
    else:
        print("❌ 所有命令都失敗")
    
    return success_count > 0

def test_webbrowser_module():
    """測試 Python webbrowser 模組"""
    print("\n=== 測試 Python webbrowser 模組 ===")
    
    try:
        import webbrowser
        
        # 創建測試頁面
        test_file = create_test_page()
        
        print(f"使用 webbrowser.open 開啟: {test_file}")
        webbrowser.open(f"file://{test_file}")
        print("✅ webbrowser.open 執行完成")
        
        # 等待瀏覽器啟動
        print("等待 3 秒讓瀏覽器啟動...")
        time.sleep(3)
        
        return True
        
    except Exception as e:
        print(f"❌ webbrowser 測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("開始測試瀏覽器是否真的啟動...")
    
    # 測試直接命令
    cmd_success = test_browser_launch()
    
    # 測試 webbrowser 模組
    web_success = test_webbrowser_module()
    
    print(f"\n=== 最終結果 ===")
    print(f"直接命令測試: {'✅ 成功' if cmd_success else '❌ 失敗'}")
    print(f"webbrowser 模組測試: {'✅ 成功' if web_success else '❌ 失敗'}")
    
    if cmd_success or web_success:
        print("\n🎉 瀏覽器啟動測試成功！")
        print("💡 如果您看到了彩色的測試頁面，說明瀏覽器確實啟動了")
        print("💡 這意味著 MCP Feedback Enhanced 的瀏覽器啟動功能應該也能工作")
        print("\n📋 現在請再次觸發 MCP feedback enhanced，並注意：")
        print("1. Cursor 右下角的端口轉發通知")
        print("2. 終端面板的「端口」標籤")
        print("3. 瀏覽器中的新標籤")
    else:
        print("\n❌ 瀏覽器啟動測試失敗")
        print("💡 這說明在當前 SSH remote 環境下確實無法自動啟動本地瀏覽器")
        print("💡 需要手動設置端口轉發或使用其他方法")
    
    # 清理測試文件
    try:
        test_file = os.path.expanduser("~/browser_test_success.html")
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n🧹 已清理測試文件: {test_file}")
    except:
        pass
